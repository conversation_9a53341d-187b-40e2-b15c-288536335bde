'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef, useState } from 'react';

interface Project {
  id: number;
  title: string;
  category: string;
  description: string;
  image: string;
  color: string;
  tags: string[];
  github: string;
  features: string[];
}

const projects: Project[] = [
  {
    id: 1,
    title: "Kaito-AI",
    category: "AI Chatbot Platform",
    description: "Intelligent search & document analysis chatbot with RAG capabilities, web search integration, and multi-threading conversation management.",
    image: "🤖",
    color: "from-blue-500/20 to-purple-500/20",
    tags: ["LangGraph", "RAG", "Streamlit", "Groq"],
    github: "https://github.com/vansh-visariya/kaito-ai",
    features: ["Web Search Mode", "Document Analysis", "Chat History", "Vector Search"]
  },
  {
    id: 2,
    title: "BhiduAI",
    category: "Localized LLM",
    description: "Cultural language model fine-tuned with Mumbai Bambaiyya slang, mixing Hindi, English, and local street language for authentic conversations.",
    image: "💬",
    color: "from-green-500/20 to-blue-500/20",
    tags: ["LoRA", "Fine-tuning", "Gemma-2", "Cultural AI"],
    github: "https://github.com/vansh-visariya/BhiduAI",
    features: ["Mumbai Slang", "Cultural Nuances", "Localized Responses", "3000+ Dataset"]
  },
  {
    id: 3,
    title: "Kaito-Model",
    category: "Transformer Implementation",
    description: "Complete GPT-2 style transformer model built from scratch using PyTorch, demonstrating core concepts of self-attention and autoregressive generation.",
    image: "🧠",
    color: "from-purple-500/20 to-pink-500/20",
    tags: ["PyTorch", "Transformer", "GPT-2", "From Scratch"],
    github: "https://github.com/vansh-visariya/kaito-model",
    features: ["Multi-Head Attention", "Layer Normalization", "Text Generation", "Training Pipeline"]
  }
];

const WorkSection = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [hoveredProject, setHoveredProject] = useState<number | null>(null);

  return (
    <section ref={ref} id="work" className="py-20 px-4 bg-black">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            Featured Projects
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            A showcase of my AI/ML projects, from intelligent chatbots to transformer models
            built from scratch, demonstrating expertise in modern AI development.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-1 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className="group cursor-pointer"
              onMouseEnter={() => setHoveredProject(project.id)}
              onMouseLeave={() => setHoveredProject(null)}
              onClick={() => window.open(project.github, '_blank')}
            >
              <div className={`bg-gradient-to-br ${project.color} backdrop-blur-sm rounded-lg p-6 h-full border border-white/10 hover:border-white/20 transition-all duration-300 group-hover:scale-105`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="text-4xl">{project.image}</div>
                  <span className="text-xs text-gray-400 bg-white/10 px-2 py-1 rounded-full">
                    {project.category}
                  </span>
                </div>

                <h3 className="text-xl font-semibold mb-3 text-white group-hover:text-green-400 transition-colors">
                  {project.title}
                </h3>

                <p className="text-gray-300 text-sm leading-relaxed mb-4">
                  {project.description}
                </p>

                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-white mb-2">Key Features:</h4>
                  <ul className="text-xs text-gray-300 space-y-1">
                    {project.features.map((feature, idx) => (
                      <li key={idx}>• {feature}</li>
                    ))}
                  </ul>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  {project.tags.map((tag) => (
                    <span
                      key={tag}
                      className="text-xs bg-white/10 text-gray-300 px-2 py-1 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <motion.div
                  className="flex items-center text-green-400 text-sm font-medium"
                  initial={{ opacity: 0, x: -10 }}
                  animate={hoveredProject === project.id ? { opacity: 1, x: 0 } : { opacity: 0, x: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  View on GitHub
                  <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <a
            href="https://github.com/vansh-visariya"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-8 py-4 border border-white text-white font-semibold rounded-full hover:bg-white hover:text-black transition-all duration-300"
          >
            View All Projects
            <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default WorkSection;
