'use client';

import { Suspense, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Environment, Float, Text3D, Center } from '@react-three/drei';
import { motion } from 'framer-motion';
import * as THREE from 'three';

// Complex Geometric Structure Component
function GeometricStructure() {
  const mainGroupRef = useRef<THREE.Group>(null);
  const innerGroupRef = useRef<THREE.Group>(null);
  const particlesRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    const time = state.clock.elapsedTime;

    if (mainGroupRef.current) {
      mainGroupRef.current.rotation.y = time * 0.1;
      mainGroupRef.current.rotation.x = Math.sin(time * 0.3) * 0.1;
    }

    if (innerGroupRef.current) {
      innerGroupRef.current.rotation.x = time * 0.2;
      innerGroupRef.current.rotation.z = time * 0.15;
    }

    if (particlesRef.current) {
      particlesRef.current.children.forEach((child, i) => {
        child.position.y = Math.sin(time + i) * 0.5;
        child.rotation.x = time + i;
        child.rotation.y = time * 0.5 + i;
      });
    }
  });

  return (
    <group ref={mainGroupRef}>
      <Float speed={1.5} rotationIntensity={0.3} floatIntensity={0.2}>
        {/* Main central structure */}
        <group ref={innerGroupRef}>
          {/* Core icosahedron */}
          <mesh position={[0, 0, 0]}>
            <icosahedronGeometry args={[1.2, 1]} />
            <meshStandardMaterial
              color="#00ff88"
              metalness={0.9}
              roughness={0.1}
              emissive="#003322"
              emissiveIntensity={0.2}
              wireframe={false}
            />
          </mesh>

          {/* Wireframe overlay */}
          <mesh position={[0, 0, 0]}>
            <icosahedronGeometry args={[1.25, 1]} />
            <meshBasicMaterial
              color="#00ff88"
              wireframe={true}
              transparent={true}
              opacity={0.3}
            />
          </mesh>

          {/* Inner rotating elements */}
          {[...Array(8)].map((_, i) => (
            <mesh
              key={i}
              position={[
                Math.cos((i / 8) * Math.PI * 2) * 0.8,
                Math.sin((i / 4) * Math.PI) * 0.3,
                Math.sin((i / 8) * Math.PI * 2) * 0.8,
              ]}
              rotation={[i, i * 0.5, i * 0.3]}
            >
              <octahedronGeometry args={[0.1, 0]} />
              <meshStandardMaterial
                color="#ffffff"
                emissive="#00ff88"
                emissiveIntensity={0.5}
              />
            </mesh>
          ))}
        </group>

        {/* Outer ring elements */}
        <group>
          {[...Array(12)].map((_, i) => (
            <mesh
              key={i}
              position={[
                Math.cos((i / 12) * Math.PI * 2) * 2.5,
                Math.sin((i / 6) * Math.PI) * 0.5,
                Math.sin((i / 12) * Math.PI * 2) * 2.5,
              ]}
            >
              <tetrahedronGeometry args={[0.08, 0]} />
              <meshStandardMaterial
                color="#00ff88"
                transparent={true}
                opacity={0.8}
                emissive="#00ff88"
                emissiveIntensity={0.3}
              />
            </mesh>
          ))}
        </group>

        {/* Floating particles */}
        <group ref={particlesRef}>
          {[...Array(20)].map((_, i) => (
            <mesh
              key={i}
              position={[
                (Math.random() - 0.5) * 8,
                (Math.random() - 0.5) * 6,
                (Math.random() - 0.5) * 8,
              ]}
            >
              <sphereGeometry args={[0.02, 8, 8]} />
              <meshBasicMaterial
                color="#ffffff"
                transparent={true}
                opacity={0.6}
              />
            </mesh>
          ))}
        </group>
      </Float>
    </group>
  );
}

// Loading component
function Loader() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-400"></div>
    </div>
  );
}

// Main Scene Component
const Scene3D = () => {
  return (
    <div className="w-full h-screen relative">
      <Canvas
        camera={{ position: [0, 0, 8], fov: 45 }}
        gl={{ antialias: true, alpha: true }}
        className="bg-transparent"
      >
        <Suspense fallback={null}>
          {/* Lighting */}
          <ambientLight intensity={0.4} />
          <directionalLight position={[10, 10, 5]} intensity={1} />
          <pointLight position={[-10, -10, -10]} intensity={0.5} color="#00ff88" />
          
          {/* Environment */}
          <Environment preset="night" />
          
          {/* 3D Objects */}
          <GeometricStructure />
          
          {/* Controls */}
          <OrbitControls
            enablePan={false}
            enableZoom={false}
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 3}
            autoRotate
            autoRotateSpeed={0.5}
          />
        </Suspense>
      </Canvas>
      
      {/* Overlay content */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 1 }}
          className="text-center text-white z-10"
        >
          <motion.h1
            className="text-6xl md:text-8xl font-bold mb-4 tracking-wider"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1.5, duration: 0.8, ease: "easeOut" }}
          >
            MANTIS
          </motion.h1>
          <motion.p
            className="text-xl md:text-2xl font-light tracking-wide opacity-80"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 2, duration: 0.6 }}
          >
            Experience Studio
          </motion.p>
          <motion.div
            className="mt-8 text-sm md:text-base opacity-60"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2.5, duration: 0.8 }}
          >
            We build to inspire, but also build to code
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 pointer-events-none"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 3, duration: 0.8 }}
      >
        <motion.div
          className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
        >
          <motion.div
            className="w-1 h-3 bg-white/50 rounded-full mt-2"
            animate={{ opacity: [0.3, 1, 0.3] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          />
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Scene3D;
